{% extends 'base.html' %}

{% block title %}Create Your Venue - Step {{ step_number }} of {{ total_steps }} - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Enhanced Venue Creation Wizard Styles -->
<style>
    :root {
        --cw-primary: #2F160F;
        --cw-accent: #fae1d7;
        --cw-success: #10b981;
        --cw-warning: #f59e0b;
        --cw-error: #ef4444;
    }

    /* Progress Bar */
    .wizard-progress {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .progress-steps {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1rem;
        position: relative;
    }

    .progress-steps::before {
        content: '';
        position: absolute;
        top: 20px;
        left: 20px;
        right: 20px;
        height: 2px;
        background: #e5e7eb;
        z-index: 1;
    }

    .progress-step {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 2;
        flex: 1;
        text-align: center;
    }

    .step-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #e5e7eb;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        color: #6b7280;
        margin-bottom: 0.5rem;
        transition: all 0.3s ease;
    }

    .step-circle.completed {
        background: var(--cw-success);
        color: white;
    }

    .step-circle.active {
        background: var(--cw-primary);
        color: white;
    }

    /* Fix for progress bar text visibility - override global black color rules */
    .wizard-progress .progress-steps .step-circle.active {
        background: var(--cw-primary) !important;
        color: white !important;
    }

    .step-title {
        font-size: 0.875rem;
        font-weight: 500;
        color: #6b7280;
    }

    .step-title.active {
        color: var(--cw-primary);
        font-weight: 600;
    }

    /* Wizard Content */
    .wizard-content {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }

    .step-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .step-header h2 {
        color: var(--cw-primary);
        font-size: 1.875rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .step-description {
        color: #6b7280;
        font-size: 1.125rem;
    }

    /* Enhanced Form Fields */
    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        display: block;
        font-weight: 600;
        color: var(--cw-primary);
        margin-bottom: 0.5rem;
    }

    .form-control {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid #e5e7eb;
        border-radius: 0.5rem;
        font-size: 1rem;
        transition: all 0.2s ease;
    }

    .form-control:focus {
        outline: none;
        border-color: var(--cw-primary);
        box-shadow: 0 0 0 3px rgba(47, 22, 15, 0.1);
    }

    .form-control.is-valid {
        border-color: var(--cw-success);
    }

    .form-control.is-invalid {
        border-color: var(--cw-error);
    }

    /* Character Counter */
    .character-counter {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 0.5rem;
        font-size: 0.875rem;
        color: #6b7280;
    }

    .character-count {
        font-weight: 500;
    }

    .character-count.warning {
        color: var(--cw-warning);
    }

    .character-count.error {
        color: var(--cw-error);
    }

    /* Real-time Validation */
    .field-feedback {
        margin-top: 0.5rem;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .field-feedback.success {
        color: var(--cw-success);
    }

    .field-feedback.error {
        color: var(--cw-error);
    }

    /* Category Selection */
    .category-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .category-card {
        border: 2px solid #e5e7eb;
        border-radius: 0.75rem;
        padding: 1.25rem;
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;
    }

    .category-card:hover {
        border-color: var(--cw-primary);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.1);
    }

    .category-card.selected {
        border-color: var(--cw-primary);
        background: var(--cw-accent);
    }

    .category-card input[type="checkbox"] {
        position: absolute;
        top: 1rem;
        right: 1rem;
        width: 1.25rem;
        height: 1.25rem;
    }

    .category-card .category-label {
        display: block;
        cursor: pointer;
        font-weight: 500;
        margin: 0;
    }

    /* Navigation Buttons */
    .wizard-navigation {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 2rem;
    }

    .btn-wizard {
        padding: 0.75rem 2rem;
        border-radius: 0.5rem;
        font-weight: 600;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-primary {
        background: var(--cw-primary);
        color: white;
    }

    .btn-primary:hover {
        background: #1f0a08;
        transform: translateY(-1px);
    }

    .btn-secondary {
        background: #6b7280;
        color: white;
    }

    .btn-secondary:hover {
        background: #4b5563;
    }

    /* Auto-save indicator styles */
    .auto-save-indicator {
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--brand-primary);
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 500;
        transform: translateX(100%);
        opacity: 0;
        transition: all 0.3s ease;
        z-index: 1050;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .auto-save-indicator.show {
        transform: translateX(0);
        opacity: 1;
    }
    
    .auto-save-indicator.saving {
        background: #ffc107;
        color: #212529;
    }
    
    .auto-save-indicator.success {
        background: #28a745;
        color: white;
    }
    
    .auto-save-indicator.error {
        background: #dc3545;
        color: white;
    }
    
    .auto-save-indicator i {
        font-size: 16px;
    }
    
    /* Draft restoration banner styles */
    .draft-restoration-banner {
        border-left: 4px solid var(--brand-primary);
        background: linear-gradient(90deg, rgba(var(--brand-primary-rgb), 0.1) 0%, transparent 100%);
        border-color: var(--brand-primary);
        margin-bottom: 2rem;
    }
    
    .draft-restoration-banner .fas {
        color: var(--brand-primary);
        font-size: 1.2em;
    }
    
    /* Enhanced progress bar */
    .wizard-progress .progress-bar {
        transition: width 0.6s ease;
    }
    
    /* Form field focus enhancement for better auto-save feedback */
    .form-control:focus {
        border-color: var(--brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(var(--brand-primary-rgb), 0.25);
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .auto-save-indicator {
            top: 10px;
            right: 10px;
            left: 10px;
            transform: translateY(-100%);
            font-size: 13px;
            padding: 10px 16px;
        }
        
        .auto-save-indicator.show {
            transform: translateY(0);
        }
        
        .draft-restoration-banner {
            margin-left: -15px;
            margin-right: -15px;
            border-radius: 0;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="venue-creation-wizard">
    <div class="container">
        <!-- Progress Bar -->
        <div class="wizard-progress">
            <div class="progress-steps">
                {% for step_key, step_name in step_choices %}
                    <div class="progress-step">
                        <div class="step-circle {% if step_key == current_step %}active{% elif step_key in completed_steps %}completed{% endif %}">
                            {% if step_key == current_step %}
                                {{ step_number }}
                            {% elif step_key in completed_steps %}
                                ✓
                            {% else %}
                                {{ forloop.counter }}
                            {% endif %}
                        </div>
                        <div class="step-title {% if step_key == current_step %}active{% endif %}">
                            {{ step_name }}
                        </div>
                    </div>
                {% endfor %}
            </div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: {{ progress_percentage }}%"></div>
            </div>
        </div>

        <!-- Wizard Content -->
        <div class="wizard-content">
            <div class="step-header">
                <h2>{{ current_step_title }}</h2>
                <p class="step-description">
                    {% if current_step == 'basic' %}
                        Let's start with the essential information about your venue including name, description, and categories.
                    {% elif current_step == 'location' %}
                        Help customers find you with accurate location, address, and contact details.
                    {% elif current_step == 'services' %}
                        Add your services with pricing information and any special discounts.
                    {% elif current_step == 'gallery' %}
                        Upload images to showcase your venue and attract customers.
                    {% elif current_step == 'details' %}
                        Set operating hours, amenities, FAQs, policies, and review all information before submitting.
                    {% endif %}
                </p>
            </div>

            <form method="post" id="wizardForm" data-step="{{ current_step }}">
                {% csrf_token %}
                
                {% if current_step == 'basic' %}
                    <div class="form-group">
                        <label class="form-label" for="{{ form.venue_name.id_for_label }}">
                            Venue Name <span class="text-danger">*</span>
                        </label>
                        {{ form.venue_name|add_class:"form-control" }}
                        <div class="field-feedback" id="venue-name-feedback"></div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="{{ form.short_description.id_for_label }}">
                            Description <span class="text-danger">*</span>
                        </label>
                        {{ form.short_description|add_class:"form-control" }}
                        <div class="character-counter">
                            <span class="character-count" id="description-count">0 / 500</span>
                            <span class="counter-hint">Minimum 10 characters</span>
                        </div>
                        <div class="field-feedback" id="description-feedback"></div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            Categories <span class="text-danger">*</span>
                        </label>
                        <p class="text-muted mb-2">Select up to 3 categories that best describe your venue</p>
                        <div class="category-grid">
                            {% for choice in form.categories %}
                                <div class="category-card">
                                    {{ choice.tag }}
                                    <label for="{{ choice.id_for_label }}" class="category-label">
                                        {{ choice.choice_label }}
                                    </label>
                                </div>
                            {% endfor %}
                        </div>
                        <div class="field-feedback" id="categories-feedback"></div>
                    </div>

                {% elif current_step == 'location' %}
                    <!-- Location Section -->
                    <div class="location-section">
                        <h5>Address Information</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label" for="{{ form.state.id_for_label }}">
                                        State <span class="text-danger">*</span>
                                    </label>
                                    {{ form.state|add_class:"form-control" }}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label" for="{{ form.county.id_for_label }}">
                                        County <span class="text-danger">*</span>
                                    </label>
                                    {{ form.county|add_class:"form-control" }}
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="{{ form.city.id_for_label }}">
                                City <span class="text-danger">*</span>
                            </label>
                            {{ form.city|add_class:"form-control" }}
                        </div>

                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label" for="{{ form.street_number.id_for_label }}">
                                        Street Number
                                    </label>
                                    {{ form.street_number|add_class:"form-control" }}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label" for="{{ form.street_name.id_for_label }}">
                                        Street Name <span class="text-danger">*</span>
                                    </label>
                                    {{ form.street_name|add_class:"form-control" }}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label" for="{{ form.zip_code.id_for_label }}">
                                        ZIP Code <span class="text-danger">*</span>
                                    </label>
                                    {{ form.zip_code|add_class:"form-control" }}
                                </div>
                            </div>
                        </div>

                        <!-- Hidden fields for coordinates -->
                        {{ form.latitude }}
                        {{ form.longitude }}
                    </div>

                    <!-- Contact Section -->
                    <div class="contact-section">
                        <h5>Contact Information</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label" for="{{ form.phone.id_for_label }}">
                                        Phone Number <span class="text-danger">*</span>
                                    </label>
                                    {{ form.phone|add_class:"form-control" }}
                                    <div class="field-feedback" id="phone-feedback"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label" for="{{ form.email.id_for_label }}">
                                        Email Address <span class="text-danger">*</span>
                                    </label>
                                    {{ form.email|add_class:"form-control" }}
                                    <div class="field-feedback" id="email-feedback"></div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="{{ form.website_url.id_for_label }}">
                                Website URL
                            </label>
                            {{ form.website_url|add_class:"form-control" }}
                        </div>

                        <div class="social-media-section">
                            <h6>Social Media Links (Optional)</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label" for="{{ form.instagram_url.id_for_label }}">
                                            <i class="fab fa-instagram"></i> Instagram
                                        </label>
                                        {{ form.instagram_url|add_class:"form-control" }}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label" for="{{ form.facebook_url.id_for_label }}">
                                            <i class="fab fa-facebook"></i> Facebook
                                        </label>
                                        {{ form.facebook_url|add_class:"form-control" }}
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label" for="{{ form.twitter_url.id_for_label }}">
                                            <i class="fab fa-twitter"></i> Twitter
                                        </label>
                                        {{ form.twitter_url|add_class:"form-control" }}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label" for="{{ form.linkedin_url.id_for_label }}">
                                            <i class="fab fa-linkedin"></i> LinkedIn
                                        </label>
                                        {{ form.linkedin_url|add_class:"form-control" }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                {% elif current_step == 'services' %}

                    <div class="services-section">
                        <h5>Your Services</h5>
                        <p class="text-muted">Add the services you offer with pricing information</p>

                        <div id="services-container">
                            <!-- Dynamic services will be generated by JavaScript -->
                        </div>

                        <button type="button" class="btn btn-outline-primary" id="add-service-btn">
                            <i class="fas fa-plus"></i> Add Service
                        </button>

                        {{ form.services }}
                    </div>

                {% elif current_step == 'gallery' %}
                    <!-- Image Gallery Section -->
                    <div class="gallery-section">
                        <h5>Venue Gallery</h5>
                        <p class="text-muted">Upload up to 5 images to showcase your venue</p>

                        <div class="image-upload-area" id="image-upload-area">
                            <div class="upload-placeholder">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <p>Drag & drop images here or click to browse</p>
                                <input type="file" id="image-upload" multiple accept="image/*" style="display: none;">
                            </div>
                            <div id="image-preview-container"></div>
                        </div>

                        {{ form.images }}
                        {{ form.main_image }}
                    </div>

                {% elif current_step == 'details' %}
                    <!-- Operating Hours Section -->
                    <div class="hours-section">
                        <h5>Operating Hours</h5>
                        <div id="operating-hours-container">
                            <!-- Dynamic operating hours will be generated by JavaScript -->
                        </div>
                        {{ form.operating_hours }}
                    </div>

                    <!-- Amenities Section -->
                    <div class="amenities-section">
                        <h5>Venue Amenities</h5>
                        <p class="text-muted">Select all amenities available at your venue</p>
                        <div class="amenities-grid">
                            {% for choice in form.amenities %}
                                <div class="amenity-option">
                                    {{ choice.tag }}
                                    <label for="{{ choice.id_for_label }}" class="amenity-label">
                                        <i class="fas fa-check"></i>
                                        {{ choice.choice_label }}
                                    </label>
                                </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- FAQs Section -->
                    <div class="faqs-section">
                        <h5>Frequently Asked Questions (Optional)</h5>
                        <p class="text-muted">Add up to 5 FAQs to help customers</p>

                        <div id="faqs-container">
                            <!-- Dynamic FAQs will be generated by JavaScript -->
                        </div>

                        <button type="button" class="btn btn-outline-primary" id="add-faq-btn">
                            <i class="fas fa-plus"></i> Add FAQ
                        </button>

                        {{ form.faqs }}
                    </div>

                    <!-- Policies Section -->
                    <div class="policies-section">
                        <h5>Policies (Optional)</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label" for="{{ form.cancellation_policy.id_for_label }}">
                                        Cancellation Policy
                                    </label>
                                    {{ form.cancellation_policy|add_class:"form-control" }}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label" for="{{ form.booking_policy.id_for_label }}">
                                        Booking Policy
                                    </label>
                                    {{ form.booking_policy|add_class:"form-control" }}
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="{{ form.special_instructions.id_for_label }}">
                                Special Instructions
                            </label>
                            {{ form.special_instructions|add_class:"form-control" }}
                        </div>
                    </div>

                    <!-- Final Review and Status Selection -->
                    <div class="review-section">
                        <h5>Review & Submit</h5>
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle"></i> Almost Done!</h6>
                            <p>Review your venue information and choose how to save it.</p>
                        </div>

                        <!-- Status Selection -->
                        <div class="form-group">
                            <label class="form-label">Choose Action</label>

                            <div class="status-options">
                                {% for choice in form.venue_status %}
                                    <div class="status-card" data-status="{{ choice.data.value }}">
                                        {{ choice.tag }}
                                        <div class="status-content">
                                            <h5>{{ choice.choice_label }}</h5>
                                            <p>
                                                {% if choice.data.value == 'draft' %}
                                                    Save privately for later editing and submit when ready.
                                                {% else %}
                                                    Submit for admin review to make visible to customers.
                                                {% endif %}
                                            </p>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>

                {% endif %}

                <!-- Navigation -->
                <div class="wizard-navigation">
                    <div>
                        {% if not is_first_step %}
                            <a href="{% url 'venues_app:venue_create_wizard' step=previous_step %}" class="btn-wizard btn-secondary">
                                <i class="fas fa-arrow-left"></i> Previous
                            </a>
                        {% endif %}
                    </div>
                    
                    <div>
                        {% if is_final_step %}
                            <button type="submit" class="btn-wizard btn-primary">
                                <i class="fas fa-check"></i> Create Venue
                            </button>
                        {% else %}
                            <button type="submit" class="btn-wizard btn-primary">
                                Next <i class="fas fa-arrow-right"></i>
                            </button>
                        {% endif %}
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Auto-save Indicator -->
    <div class="auto-save-indicator" id="autoSaveIndicator">
        <i class="fas fa-save"></i> <span id="autoSaveText">Progress saved automatically</span>
    </div>
    
    <!-- Draft Restoration Banner -->
    {% if has_draft and draft_updated_at %}
    <div class="draft-restoration-banner alert alert-info alert-dismissible fade show" role="alert">
        <div class="d-flex align-items-center">
            <i class="fas fa-info-circle me-2"></i>
            <div>
                <strong>Draft restored!</strong> Your progress was saved {{ draft_updated_at|timesince }} ago.
                <small class="d-block text-muted">Changes are automatically saved as you type.</small>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    </div>
    {% endif %}
</section>

<script>
// Enhanced venue creation wizard functionality with database-backed auto-save
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('wizardForm');
    const currentStep = form.dataset.step;
    
    // Auto-save functionality
    let autoSaveTimeout;
    let lastSaveData = {};
    const autoSaveDelay = 3000; // 3 seconds
    const autoSaveIndicator = document.getElementById('autoSaveIndicator');
    const autoSaveText = document.getElementById('autoSaveText');
    
    function autoSave() {
        const formData = new FormData(form);
        formData.append('action', 'save_progress');
        
        // Check if data has changed
        const currentData = Object.fromEntries(formData.entries());
        if (JSON.stringify(currentData) === JSON.stringify(lastSaveData)) {
            return; // No changes, skip save
        }
        
        // Show saving indicator
        showAutoSaveIndicator('Saving...', 'saving');
        
        fetch(window.location.href, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                lastSaveData = currentData;
                showAutoSaveIndicator('Progress saved', 'success');
                
                // Update progress bar if available
                const progressBar = document.querySelector('.wizard-progress .progress-bar');
                if (progressBar && data.progress_percentage) {
                    progressBar.style.width = data.progress_percentage + '%';
                    progressBar.setAttribute('aria-valuenow', data.progress_percentage);
                }
            } else {
                showAutoSaveIndicator('Save failed', 'error');
                console.error('Auto-save failed:', data.message);
            }
        })
        .catch(error => {
            showAutoSaveIndicator('Save failed', 'error');
            console.error('Auto-save error:', error);
        });
    }
    
    function showAutoSaveIndicator(text, type = 'success') {
        autoSaveText.textContent = text;
        autoSaveIndicator.className = 'auto-save-indicator show';
        
        // Add type-specific styling
        if (type === 'saving') {
            autoSaveIndicator.classList.add('saving');
        } else if (type === 'error') {
            autoSaveIndicator.classList.add('error');
        } else {
            autoSaveIndicator.classList.add('success');
        }
        
        // Auto-hide after 3 seconds for success/error
        if (type !== 'saving') {
            setTimeout(() => {
                autoSaveIndicator.classList.remove('show');
                // Reset classes
                autoSaveIndicator.classList.remove('saving', 'error', 'success');
            }, 3000);
        }
    }
    
    function scheduleAutoSave() {
        clearTimeout(autoSaveTimeout);
        autoSaveTimeout = setTimeout(autoSave, autoSaveDelay);
    }
    
    // Set up auto-save listeners
    if (form) {
        // Save on input changes
        form.addEventListener('input', scheduleAutoSave);
        form.addEventListener('change', scheduleAutoSave);
        
        // Save before page unload
        window.addEventListener('beforeunload', function(e) {
            // Only auto-save if there are unsaved changes
            const formData = new FormData(form);
            const currentData = Object.fromEntries(formData.entries());
            
            if (JSON.stringify(currentData) !== JSON.stringify(lastSaveData)) {
                // Use navigator.sendBeacon for reliable saving on page unload
                const beaconData = new FormData(form);
                beaconData.append('action', 'save_progress');
                
                navigator.sendBeacon(window.location.href, beaconData);
            }
        });
        
        // Initial save to establish baseline
        setTimeout(() => {
            const formData = new FormData(form);
            lastSaveData = Object.fromEntries(formData.entries());
        }, 500);
    }
    
    // Real-time validation
    function validateField(fieldName, fieldValue) {
        const formData = new FormData();
        formData.append('action', 'validate_field');
        formData.append('field_name', fieldName);
        formData.append('field_value', fieldValue);
        formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);
        
        fetch(window.location.href, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            const feedback = document.getElementById(fieldName.replace('_', '-') + '-feedback');
            if (feedback) {
                if (data.is_valid) {
                    feedback.innerHTML = '<i class="fas fa-check"></i> Looks good!';
                    feedback.className = 'field-feedback success';
                } else {
                    feedback.innerHTML = '<i class="fas fa-times"></i> ' + data.errors.join(', ');
                    feedback.className = 'field-feedback error';
                }
            }
        })
        .catch(error => console.error('Validation error:', error));
    }
    
    // Setup field listeners
    const watchedFields = ['venue_name', 'short_description', 'phone', 'email', 'website_url'];
    watchedFields.forEach(fieldName => {
        const field = document.getElementById('id_' + fieldName);
        if (field) {
            field.addEventListener('input', function() {
                // Clear previous timeout
                clearTimeout(autoSaveTimeout);
                
                // Real-time validation
                if (this.value.trim()) {
                    validateField(fieldName, this.value);
                }
                
                // Character counter
                if (fieldName === 'short_description') {
                    updateCharacterCounter(this);
                }
                
                // Auto-save
                scheduleAutoSave();
            });
        }
    });
    
    // Character counter
    function updateCharacterCounter(field) {
        const counter = document.getElementById('description-count');
        const maxLength = 500;
        const currentLength = field.value.length;
        
        if (counter) {
            counter.textContent = currentLength + ' / ' + maxLength;
            
            if (currentLength > maxLength * 0.9) {
                counter.className = 'character-count warning';
            } else if (currentLength > maxLength) {
                counter.className = 'character-count error';
            } else {
                counter.className = 'character-count';
            }
        }
    }
    
    // Category selection
    document.querySelectorAll('.category-card').forEach(card => {
        card.addEventListener('click', function() {
            const checkbox = this.querySelector('input[type="checkbox"]');
            if (checkbox) {
                checkbox.checked = !checkbox.checked;
                this.classList.toggle('selected', checkbox.checked);
                
                // Limit to 3 selections
                const selected = document.querySelectorAll('.category-card input[type="checkbox"]:checked');
                if (selected.length > 3) {
                    checkbox.checked = false;
                    this.classList.remove('selected');
                    alert('You can select up to 3 categories only.');
                }
                
                // Auto-save
                scheduleAutoSave();
            }
        });
    });
    
    // Status selection
    document.querySelectorAll('.status-card').forEach(card => {
        card.addEventListener('click', function() {
            const radio = this.querySelector('input[type="radio"]');
            if (radio) {
                // Remove selected class from all
                document.querySelectorAll('.status-card').forEach(c => c.classList.remove('selected'));
                
                // Select this one
                radio.checked = true;
                this.classList.add('selected');
            }
        });
    });
    
    // Initialize character counter
    const descField = document.getElementById('id_short_description');
    if (descField) {
        updateCharacterCounter(descField);
    }
});
</script>

{% if show_guided_tour %}
<!-- Guided Tour Integration -->
<script src="{% static 'js/intro.min.js' %}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const tourSteps = {{ tour_steps|safe }};
    
    if (tourSteps && tourSteps.length > 0) {
        const intro = introJs();
        
        intro.setOptions({
            steps: tourSteps.map(step => ({
                element: step.target,
                intro: `<h4>${step.title}</h4><p>${step.content}</p>`,
                position: step.placement || 'bottom'
            })),
            showProgress: true,
            showBullets: false,
            exitOnOverlayClick: false,
            disableInteraction: false
        });
        
        // Show tour after a brief delay
        setTimeout(() => {
            intro.start();
        }, 1000);
    }
});
</script>
{% endif %}
{% endblock %} 